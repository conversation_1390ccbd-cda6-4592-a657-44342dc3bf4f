# 服务器配置
SERVER_PORT=8080
GIN_MODE=debug

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=solve_api
DB_CHARSET=utf8mb4

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_here

# 短信配置
SMS_ACCESS_KEY_ID=your_aliyun_access_key_id
SMS_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
SMS_SIGN_NAME=your_sms_sign_name
SMS_TEMPLATE_CODE=your_sms_template_code

# 模型配置
MODEL_OCR=qwen-vl-plus
MODEL_SOLVE=qwen-plus

# 模型API密钥（需要在数据库中配置对应的模型记录）
QWEN_API_KEY=your_qwen_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
