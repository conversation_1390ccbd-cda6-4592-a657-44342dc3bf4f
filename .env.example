# 服务器配置
SERVER_PORT=8080
GIN_MODE=debug

# 数据库配置
DB_HOST=***********
DB_PORT=3380
DB_USERNAME=gmdns
DB_PASSWORD=Suyan15913..
DB_DATABASE=solve_web
DB_CHARSET=utf8mb4

# Redis配置
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=Suyan15913..
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_here

# 短信配置
SMS_ACCESS_KEY_ID=LTAI5tFY8uwqiKHQ5Q9AHwg6
SMS_ACCESS_KEY_SECRET=******************************
SMS_SIGN_NAME=青岛果沐云计算
SMS_TEMPLATE_CODE=SMS_465896116

# 模型配置
MODEL_OCR=qwen-vl-plus
MODEL_SOLVE=qwen-plus

# 模型API密钥（需要在数据库中配置对应的模型记录）
QWEN_API_KEY=your_qwen_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
