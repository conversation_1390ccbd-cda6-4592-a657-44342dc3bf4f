package main

import (
	"log"
	"solve-go-api/internal/config"
	"solve-go-api/internal/database"
	"solve-go-api/internal/router"
	"solve-go-api/internal/services"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库连接
	db, err := database.InitMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to MySQL: %v", err)
	}

	// 获取底层sql.DB用于关闭连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB: %v", err)
	}
	defer sqlDB.Close()

	// 初始化Redis连接
	rdb, err := database.InitRedis(cfg.Redis)
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	defer rdb.Close()

	// 运行数据库迁移
	if err := database.Migrate(db); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化系统数据
	if err := database.InitSystemData(db); err != nil {
		log.Fatalf("Failed to initialize system data: %v", err)
	}

	// 初始化服务
	serviceContainer := services.NewContainer(db, rdb, cfg)

	// 设置路由
	r := router.Setup(serviceContainer)

	// 启动服务器
	log.Printf("Server starting on port %s", cfg.Server.Port)
	if err := r.Run(":" + cfg.Server.Port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
