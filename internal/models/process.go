package models

import (
	"encoding/json"
	"time"
)

// ProcessContext 请求处理上下文
type ProcessContext struct {
	// 请求信息
	AppID     string    `json:"app_id"`
	UserURL   string    `json:"user_url"`
	StartTime time.Time `json:"start_time"`

	// 应用和用户信息
	App  *App  `json:"app,omitempty"`
	User *User `json:"user,omitempty"`

	// OCR处理结果
	OCRToken int `json:"ocr_token"`

	// 题目信息
	SaveQuestion *SaveQuestion `json:"save_question,omitempty"`

	// 响应信息
	Source   string          `json:"source"`   // redis, mysql, match, ai
	Response json.RawMessage `json:"response"` // 最终响应数据
	Status   int             `json:"status"`   // 1成功 0失败
	Message  string          `json:"message"`  // 错误信息
}

// SaveQuestion 保存的题目信息
type SaveQuestion struct {
	Type         string                 `json:"type"`          // 题目类型
	Content      string                 `json:"content"`       // 原始题干
	CleanContent string                 `json:"clean_content"` // 清洗后题干
	Options      map[string]string      `json:"options"`       // 选项
	Answer       interface{}            `json:"answer"`        // 答案(可能是字符串或数组)
	Analysis     string                 `json:"analysis"`      // 解析
	UserURL      string                 `json:"user_url"`      // 用户图片
	QuestionLen  int                    `json:"question_len"`  // 字符长度
	HashKey      string                 `json:"hash_key"`      // 哈希键
}

// OCRResponse OCR模型响应结构
type OCRResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		TotalTokens int `json:"total_tokens"`
	} `json:"usage"`
}

// OCRParsedData OCR解析后的数据
type OCRParsedData struct {
	QuText  string            `json:"qutext"`  // 题干文本
	Options map[string]string `json:"options"` // 选项
}

// SolveResponse Solve模型响应结构
type SolveResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		TotalTokens int `json:"total_tokens"`
	} `json:"usage"`
}

// SolveParsedData Solve解析后的数据
type SolveParsedData struct {
	Answer   interface{} `json:"answer"`   // 答案
	Analysis string      `json:"analysis"` // 解析
}

// APIRequest API请求结构
type APIRequest struct {
	AppID     string `json:"app_id" binding:"required"`
	AppSecret string `json:"app_secret" binding:"required"`
	ImageURL  string `json:"image_url" binding:"required,url"`
}

// APIResponse API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// QuestionResponse 题目响应结构
type QuestionResponse struct {
	ID       uint                   `json:"id"`
	Type     string                 `json:"type"`
	Content  string                 `json:"content"`
	Options  map[string]string      `json:"options"`
	Answer   interface{}            `json:"answer"`
	Analysis string                 `json:"analysis"`
	ImageURL string                 `json:"image_url"`
	UserURL  string                 `json:"user_url"`
}

// GetLatency 获取处理耗时(毫秒)
func (ctx *ProcessContext) GetLatency() int64 {
	return time.Since(ctx.StartTime).Milliseconds()
}

// SetError 设置错误状态
func (ctx *ProcessContext) SetError(message string) {
	ctx.Status = 0
	ctx.Message = message
}

// SetSuccess 设置成功状态
func (ctx *ProcessContext) SetSuccess(source string, response json.RawMessage) {
	ctx.Status = 1
	ctx.Source = source
	ctx.Response = response
	ctx.Message = "success"
}
