package handlers

import (
	"encoding/json"
	"net/http"
	"solve-go-api/internal/models"
	"solve-go-api/internal/services"

	"github.com/gin-gonic/gin"
)

// SolveHandler 解题处理器
type SolveHandler struct {
	solveService *services.SolveService
	appService   *services.AppService
	logService   *services.LogService
}

// NewSolveHandler 创建解题处理器
func NewSolveHandler(solveService *services.SolveService, appService *services.AppService, logService *services.LogService) *SolveHandler {
	return &SolveHandler{
		solveService: solveService,
		appService:   appService,
		logService:   logService,
	}
}

// SolveQuestion 解题接口
func (h *SolveHandler) SolveQuestion(c *gin.Context) {
	var request models.APIRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 处理解题请求
	ctx := h.solveService.ProcessSolveRequest(&request)

	// 记录请求日志
	if err := h.logService.CreateSolveLog(ctx); err != nil {
		// 日志记录失败不影响主流程，只记录错误
		// 可以考虑使用异步日志记录
	}

	// 如果成功，增加调用次数并扣除积分
	if ctx.Status == 1 {
		// 增加应用调用次数
		h.appService.IncrementCallCount(ctx.AppID)

		// 扣除用户积分 (OCR token * 2)
		if ctx.OCRToken > 0 {
			deductAmount := int64(ctx.OCRToken * 2)
			h.appService.DeductUserBalance(
				ctx.User.ID,
				deductAmount,
				"API调用扣费",
				0, // 系统操作
			)
		}

		// 解析响应数据
		var responseData []models.QuestionResponse
		if err := json.Unmarshal(ctx.Response, &responseData); err == nil {
			c.JSON(http.StatusOK, models.APIResponse{
				Code:    200,
				Message: "success",
				Data:    responseData,
			})
		} else {
			c.JSON(http.StatusOK, models.APIResponse{
				Code:    200,
				Message: "success",
				Data:    json.RawMessage(ctx.Response),
			})
		}
	} else {
		// 请求失败
		c.JSON(http.StatusOK, models.APIResponse{
			Code:    400,
			Message: ctx.Message,
		})
	}
}
