package services

import (
	"encoding/json"
	"solve-go-api/internal/models"

	"gorm.io/gorm"
)

// QuestionService 题库服务
type QuestionService struct {
	db *gorm.DB
}

// NewQuestionService 创建题库服务
func NewQuestionService(db *gorm.DB) *QuestionService {
	return &QuestionService{db: db}
}

// GetQuestionsByHashKey 根据哈希键获取题目
func (s *QuestionService) GetQuestionsByHashKey(hashKey string) ([]models.QuestionBank, error) {
	var questions []models.QuestionBank
	err := s.db.Where("hash_key = ?", hashKey).Find(&questions).Error
	return questions, err
}

// SaveQuestion 保存题目到数据库
func (s *QuestionService) SaveQuestion(saveQuestion *models.SaveQuestion) (*models.QuestionBank, error) {
	// 转换选项为JSON
	optionsJSON, err := json.Marshal(saveQuestion.Options)
	if err != nil {
		return nil, err
	}
	
	// 转换答案为JSON
	answerJSON, err := json.Marshal(saveQuestion.Answer)
	if err != nil {
		return nil, err
	}
	
	// 获取题目类型代码
	typeCode := s.getQuestionTypeCode(saveQuestion.Type)
	
	question := &models.QuestionBank{
		HashKey:      saveQuestion.HashKey,
		Type:         typeCode,
		Content:      saveQuestion.Content,
		ContentClean: saveQuestion.CleanContent,
		Options:      optionsJSON,
		Answer:       answerJSON,
		Analysis:     saveQuestion.Analysis,
		UserURL:      saveQuestion.UserURL,
		Verified:     false, // 默认未验证
		QuestionLen:  saveQuestion.QuestionLen,
	}
	
	err = s.db.Create(question).Error
	if err != nil {
		return nil, err
	}
	
	return question, nil
}

// UpdateQuestion 更新题目
func (s *QuestionService) UpdateQuestion(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.QuestionBank{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteQuestion 删除题目
func (s *QuestionService) DeleteQuestion(id uint) error {
	return s.db.Delete(&models.QuestionBank{}, id).Error
}

// GetQuestionByID 根据ID获取题目
func (s *QuestionService) GetQuestionByID(id uint) (*models.QuestionBank, error) {
	var question models.QuestionBank
	err := s.db.First(&question, id).Error
	if err != nil {
		return nil, err
	}
	return &question, nil
}

// GetQuestions 获取题目列表
func (s *QuestionService) GetQuestions(page, pageSize int, filters map[string]interface{}) ([]models.QuestionBank, int64, error) {
	var questions []models.QuestionBank
	var total int64
	
	query := s.db.Model(&models.QuestionBank{})
	
	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "type":
			query = query.Where("type = ?", value)
		case "verified":
			query = query.Where("verified = ?", value)
		case "content":
			query = query.Where("content LIKE ?", "%"+value.(string)+"%")
		}
	}
	
	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}
	
	return questions, total, nil
}

// VerifyQuestion 验证题目
func (s *QuestionService) VerifyQuestion(id uint) error {
	return s.db.Model(&models.QuestionBank{}).Where("id = ?", id).Update("verified", true).Error
}

// UnverifyQuestion 取消验证题目
func (s *QuestionService) UnverifyQuestion(id uint) error {
	return s.db.Model(&models.QuestionBank{}).Where("id = ?", id).Update("verified", false).Error
}

// GetQuestionStats 获取题库统计信息
func (s *QuestionService) GetQuestionStats() (map[string]interface{}, error) {
	var total int64
	var verified int64
	var unverified int64
	
	// 总题目数
	err := s.db.Model(&models.QuestionBank{}).Count(&total).Error
	if err != nil {
		return nil, err
	}
	
	// 已验证题目数
	err = s.db.Model(&models.QuestionBank{}).Where("verified = ?", true).Count(&verified).Error
	if err != nil {
		return nil, err
	}
	
	// 未验证题目数
	err = s.db.Model(&models.QuestionBank{}).Where("verified = ?", false).Count(&unverified).Error
	if err != nil {
		return nil, err
	}
	
	// 按类型统计
	var typeStats []map[string]interface{}
	err = s.db.Model(&models.QuestionBank{}).
		Select("type, COUNT(*) as count").
		Group("type").
		Scan(&typeStats).Error
	if err != nil {
		return nil, err
	}
	
	stats := map[string]interface{}{
		"total":      total,
		"verified":   verified,
		"unverified": unverified,
		"by_type":    typeStats,
	}
	
	return stats, nil
}

// getQuestionTypeCode 获取题目类型代码
func (s *QuestionService) getQuestionTypeCode(questionType string) int {
	switch questionType {
	case "判断题":
		return 1
	case "单选题":
		return 2
	case "多选题":
		return 3
	default:
		return 0
	}
}
