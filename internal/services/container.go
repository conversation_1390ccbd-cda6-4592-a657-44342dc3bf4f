package services

import (
	"solve-go-api/internal/config"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Container 服务容器
type Container struct {
	// 基础服务
	ModelService    *ModelService
	ParserService   *ParserService
	CacheService    *CacheService
	QuestionService *QuestionService
	MatchService    *MatchService
	AppService      *AppService
	LogService      *LogService
	UserService     *UserService

	// 核心业务服务
	SolveService *SolveService
}

// NewContainer 创建服务容器
func NewContainer(db *gorm.DB, rdb *redis.Client, cfg *config.Config) *Container {
	// 初始化基础服务
	modelService := NewModelService(db)
	parserService := NewParserService()
	cacheService := NewCacheService(rdb)
	questionService := NewQuestionService(db)
	matchService := NewMatchService(db)
	appService := NewAppService(db)
	logService := NewLogService(db)
	userService := NewUserService(db)

	// 初始化核心业务服务
	solveService := NewSolveService(
		db,
		cfg,
		modelService,
		parserService,
		cacheService,
		questionService,
		matchService,
		appService,
		logService,
	)

	return &Container{
		ModelService:    modelService,
		ParserService:   parserService,
		CacheService:    cacheService,
		QuestionService: questionService,
		MatchService:    matchService,
		AppService:      appService,
		LogService:      logService,
		UserService:     userService,
		SolveService:    solveService,
	}
}
