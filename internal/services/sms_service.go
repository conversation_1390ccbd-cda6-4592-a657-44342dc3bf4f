package services

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"solve-go-api/internal/config"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
)

// SMSService 短信服务
type SMSService struct {
	cfg *config.Config
	rdb *redis.Client
}

// NewSMSService 创建短信服务
func NewSMSService(cfg *config.Config, rdb *redis.Client) *SMSService {
	return &SMSService{
		cfg: cfg,
		rdb: rdb,
	}
}

// SendVerificationCode 发送验证码
func (s *SMSService) SendVerificationCode(phone string) (string, error) {
	// 生成6位验证码
	code := s.generateCode()

	// 发送短信
	err := s.sendSMS(phone, code)
	if err != nil {
		return "", fmt.Errorf("发送短信失败: %w", err)
	}

	// 存储验证码到Redis，5分钟过期
	ctx := context.Background()
	key := fmt.Sprintf("sms_code:%s", phone)
	err = s.rdb.Set(ctx, key, code, 5*time.Minute).Err()
	if err != nil {
		return "", fmt.Errorf("存储验证码失败: %w", err)
	}

	return code, nil
}

// VerifyCode 验证验证码
func (s *SMSService) VerifyCode(phone, code string) bool {
	ctx := context.Background()
	key := fmt.Sprintf("sms_code:%s", phone)
	storedCode, err := s.rdb.Get(ctx, key).Result()
	if err != nil {
		return false
	}

	// 验证成功后删除验证码
	if storedCode == code {
		s.rdb.Del(ctx, key)
		return true
	}

	return false
}

// generateCode 生成6位数字验证码
func (s *SMSService) generateCode() string {
	rand.Seed(time.Now().UnixNano())
	code := rand.Intn(900000) + 100000
	return strconv.Itoa(code)
}

// sendSMS 发送短信
func (s *SMSService) sendSMS(phone, code string) error {
	// 阿里云短信API参数
	params := map[string]string{
		"Action":           "SendSms",
		"Version":          "2017-05-25",
		"RegionId":         "cn-hangzhou",
		"PhoneNumbers":     phone,
		"SignName":         s.cfg.SMS.SignName,
		"TemplateCode":     s.cfg.SMS.TemplateCode,
		"TemplateParam":    fmt.Sprintf(`{"code":"%s"}`, code),
		"Format":           "JSON",
		"Timestamp":        time.Now().UTC().Format("2006-01-02T15:04:05Z"),
		"SignatureMethod":  "HMAC-SHA1",
		"SignatureVersion": "1.0",
		"SignatureNonce":   s.generateNonce(),
		"AccessKeyId":      s.cfg.SMS.AccessKeyID,
	}

	// 生成签名
	signature := s.generateSignature(params, s.cfg.SMS.AccessKeySecret)
	params["Signature"] = signature

	// 构建请求URL
	apiURL := "https://dysmsapi.aliyuncs.com/?" + s.buildQueryString(params)

	// 发送HTTP请求
	resp, err := http.Get(apiURL)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return err
	}

	// 检查发送结果
	if code, ok := result["Code"].(string); ok && code == "OK" {
		return nil
	}

	return fmt.Errorf("短信发送失败: %s", string(body))
}

// generateNonce 生成随机字符串
func (s *SMSService) generateNonce() string {
	rand.Seed(time.Now().UnixNano())
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 16)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateSignature 生成阿里云API签名
func (s *SMSService) generateSignature(params map[string]string, accessKeySecret string) string {
	// 排序参数
	var keys []string
	for k := range params {
		if k != "Signature" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建待签名字符串
	var parts []string
	for _, k := range keys {
		parts = append(parts, url.QueryEscape(k)+"="+url.QueryEscape(params[k]))
	}

	stringToSign := "GET&" + url.QueryEscape("/") + "&" + url.QueryEscape(strings.Join(parts, "&"))

	// HMAC-SHA1签名
	key := accessKeySecret + "&"
	mac := hmac.New(sha1.New, []byte(key))
	mac.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	return signature
}

// buildQueryString 构建查询字符串
func (s *SMSService) buildQueryString(params map[string]string) string {
	var parts []string
	for k, v := range params {
		parts = append(parts, url.QueryEscape(k)+"="+url.QueryEscape(v))
	}
	return strings.Join(parts, "&")
}
