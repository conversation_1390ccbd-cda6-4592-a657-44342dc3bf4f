package services

import (
	"encoding/json"
	"fmt"
	"solve-go-api/internal/config"
	"solve-go-api/internal/models"
	"time"

	"gorm.io/gorm"
)

// SolveService 核心解题服务
type SolveService struct {
	db             *gorm.DB
	cfg            *config.Config
	modelService   *ModelService
	parserService  *ParserService
	cacheService   *CacheService
	questionService *QuestionService
	matchService   *MatchService
	appService     *AppService
	logService     *LogService
}

// NewSolveService 创建解题服务
func NewSolveService(
	db *gorm.DB,
	cfg *config.Config,
	modelService *ModelService,
	parserService *ParserService,
	cacheService *CacheService,
	questionService *QuestionService,
	matchService *MatchService,
	appService *AppService,
	logService *LogService,
) *SolveService {
	return &SolveService{
		db:              db,
		cfg:             cfg,
		modelService:    modelService,
		parserService:   parserService,
		cacheService:    cacheService,
		questionService: questionService,
		matchService:    matchService,
		appService:      appService,
		logService:      logService,
	}
}

// ProcessSolveRequest 处理解题请求
func (s *SolveService) ProcessSolveRequest(request *models.APIRequest) *models.ProcessContext {
	ctx := &models.ProcessContext{
		AppID:     request.AppID,
		UserURL:   request.ImageURL,
		StartTime: time.Now(),
		Status:    0,
	}

	// 1. 验证应用和用户
	if err := s.validateAppAndUser(ctx); err != nil {
		ctx.SetError(err.Error())
		return ctx
	}

	// 2. 检查用户积分
	if ctx.User.Balance <= 0 {
		ctx.SetError("用户积分不足")
		return ctx
	}

	// 3. 调用OCR模型识别图片
	if err := s.processOCR(ctx); err != nil {
		ctx.SetError(err.Error())
		return ctx
	}

	// 4. 检查Redis缓存
	if s.checkRedisCache(ctx) {
		return ctx
	}

	// 5. 检查MySQL精确匹配
	if s.checkMySQLExactMatch(ctx) {
		return ctx
	}

	// 6. 检查MySQL模糊匹配
	if s.checkMySQLFuzzyMatch(ctx) {
		return ctx
	}

	// 7. 调用Solve模型解答
	if err := s.processSolve(ctx); err != nil {
		ctx.SetError(err.Error())
		return ctx
	}

	// 8. 保存题目到数据库并回写缓存
	if err := s.saveAndCache(ctx); err != nil {
		ctx.SetError(err.Error())
		return ctx
	}

	return ctx
}

// validateAppAndUser 验证应用和用户
func (s *SolveService) validateAppAndUser(ctx *models.ProcessContext) error {
	app, err := s.appService.GetAppByID(ctx.AppID)
	if err != nil {
		return fmt.Errorf("应用不存在")
	}

	if app.Status != 0 {
		return fmt.Errorf("应用已被禁用")
	}

	user, err := s.appService.GetUserByID(app.UserID)
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	if !user.IsActive {
		return fmt.Errorf("用户已被禁用")
	}

	ctx.App = app
	ctx.User = user
	return nil
}

// processOCR 处理OCR识别
func (s *SolveService) processOCR(ctx *models.ProcessContext) error {
	// 调用OCR模型
	response, err := s.modelService.CallOCRModel(s.cfg.Models.OCRModel, ctx.UserURL)
	if err != nil {
		return fmt.Errorf("OCR模型调用失败: %w", err)
	}

	// 记录token消耗
	ctx.OCRToken = response.Usage.TotalTokens

	// 解析OCR响应
	saveQuestion, err := s.parserService.ParseOCRResponse(response, ctx.UserURL)
	if err != nil {
		return err
	}

	ctx.SaveQuestion = saveQuestion
	return nil
}

// checkRedisCache 检查Redis缓存
func (s *SolveService) checkRedisCache(ctx *models.ProcessContext) bool {
	questions, err := s.cacheService.GetQuestionsByHashKey(ctx.SaveQuestion.HashKey)
	if err != nil || len(questions) == 0 {
		return false
	}

	// 缓存命中
	responseData, _ := json.Marshal(questions)
	ctx.SetSuccess("redis", responseData)
	return true
}

// checkMySQLExactMatch 检查MySQL精确匹配
func (s *SolveService) checkMySQLExactMatch(ctx *models.ProcessContext) bool {
	questions, err := s.questionService.GetQuestionsByHashKey(ctx.SaveQuestion.HashKey)
	if err != nil || len(questions) == 0 {
		return false
	}

	// 转换为响应格式
	responses := s.parserService.ConvertToQuestionResponse(questions)
	responseData, _ := json.Marshal(responses)

	// 回写Redis缓存
	s.cacheService.SetQuestionCache(ctx.SaveQuestion.HashKey, responses)

	ctx.SetSuccess("mysql", responseData)
	return true
}

// checkMySQLFuzzyMatch 检查MySQL模糊匹配
func (s *SolveService) checkMySQLFuzzyMatch(ctx *models.ProcessContext) bool {
	questions, err := s.matchService.MatchQuestion(ctx.SaveQuestion)
	if err != nil || len(questions) == 0 {
		return false
	}

	// 转换为响应格式
	responses := s.parserService.ConvertToQuestionResponse(questions)
	responseData, _ := json.Marshal(responses)

	ctx.SetSuccess("match", responseData)
	return true
}

// processSolve 处理Solve模型解答
func (s *SolveService) processSolve(ctx *models.ProcessContext) error {
	// 构建题目文本
	questionText := fmt.Sprintf("题目类型：%s\n题干：%s\n选项：%v",
		ctx.SaveQuestion.Type,
		ctx.SaveQuestion.Content,
		ctx.SaveQuestion.Options,
	)

	// 调用Solve模型
	response, err := s.modelService.CallSolveModel(s.cfg.Models.SolveModel, questionText)
	if err != nil {
		return fmt.Errorf("Solve模型调用失败: %w", err)
	}

	// 解析Solve响应
	solveData, err := s.parserService.ParseSolveResponse(response)
	if err != nil {
		return err
	}

	// 更新SaveQuestion
	ctx.SaveQuestion.Answer = solveData.Answer
	ctx.SaveQuestion.Analysis = solveData.Analysis

	return nil
}

// saveAndCache 保存题目并缓存
func (s *SolveService) saveAndCache(ctx *models.ProcessContext) error {
	// 保存到数据库
	question, err := s.questionService.SaveQuestion(ctx.SaveQuestion)
	if err != nil {
		return fmt.Errorf("保存题目失败: %w", err)
	}

	// 查询所有相同hash_key的题目
	allQuestions, err := s.questionService.GetQuestionsByHashKey(ctx.SaveQuestion.HashKey)
	if err != nil {
		return err
	}

	// 转换为响应格式
	responses := s.parserService.ConvertToQuestionResponse(allQuestions)
	responseData, _ := json.Marshal(responses)

	// 回写Redis缓存
	s.cacheService.SetQuestionCache(ctx.SaveQuestion.HashKey, responses)

	ctx.SetSuccess("ai", responseData)
	
	// 记录匹配的题目ID
	if len(allQuestions) > 0 {
		ctx.SaveQuestion.HashKey = fmt.Sprintf("%d", question.ID)
	}

	return nil
}
