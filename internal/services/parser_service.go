package services

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"regexp"
	"solve-go-api/internal/models"
	"strings"
)

// ParserService 解析服务
type ParserService struct{}

// NewParserService 创建解析服务
func NewParserService() *ParserService {
	return &ParserService{}
}

// ParseOCRResponse 解析OCR模型响应
func (s *ParserService) ParseOCRResponse(response *models.OCRResponse, userURL string) (*models.SaveQuestion, error) {
	if len(response.Output.Choices) == 0 {
		return nil, fmt.Errorf("no choices in OCR response")
	}

	content := response.Output.Choices[0].Message.Content
	
	// 尝试解析JSON格式的响应
	var ocrData models.OCRParsedData
	if err := json.Unmarshal([]byte(content), &ocrData); err != nil {
		return nil, fmt.Errorf("failed to parse OCR response JSON: %w", err)
	}

	// 检测题目类型
	questionType := s.detectQuestionType(ocrData.QuText)
	if questionType == "" {
		return nil, fmt.Errorf("图片解析失败，请重新拍摄标准图片")
	}

	// 清洗题干内容
	cleanedContent := s.cleanQuestionContent(ocrData.QuText)
	
	// 清洗选项
	cleanedOptions := s.cleanOptions(ocrData.Options)

	// 计算字符长度
	questionLen := len([]rune(cleanedContent))

	// 生成哈希键
	hashKey := s.generateHashKey(cleanedContent)

	saveQuestion := &models.SaveQuestion{
		Type:         questionType,
		Content:      ocrData.QuText,
		CleanContent: cleanedContent,
		Options:      cleanedOptions,
		UserURL:      userURL,
		QuestionLen:  questionLen,
		HashKey:      hashKey,
	}

	return saveQuestion, nil
}

// ParseSolveResponse 解析Solve模型响应
func (s *ParserService) ParseSolveResponse(response *models.SolveResponse) (*models.SolveParsedData, error) {
	if len(response.Output.Choices) == 0 {
		return nil, fmt.Errorf("no choices in solve response")
	}

	content := response.Output.Choices[0].Message.Content
	
	// 尝试解析JSON格式的响应
	var solveData models.SolveParsedData
	if err := json.Unmarshal([]byte(content), &solveData); err != nil {
		return nil, fmt.Errorf("failed to parse solve response JSON: %w", err)
	}

	return &solveData, nil
}

// detectQuestionType 检测题目类型
func (s *ParserService) detectQuestionType(content string) string {
	content = strings.ToLower(content)
	
	if strings.Contains(content, "单选题") {
		return "单选题"
	}
	if strings.Contains(content, "多选题") {
		return "多选题"
	}
	if strings.Contains(content, "判断题") {
		return "判断题"
	}
	if strings.Contains(content, "填空题") {
		return "填空题"
	}
	
	return ""
}

// cleanQuestionContent 清洗题干内容
func (s *ParserService) cleanQuestionContent(content string) string {
	// 使用正则表达式清洗前缀
	re := regexp.MustCompile(`^(（?\(?\s*(单选题|多选题|判断题|填空题)?\)?）?\s*\d{1,3}[、.．]?\s*)`)
	cleaned := re.ReplaceAllString(content, "")
	
	// 清洗标点符号、空格、换行符
	punctRe := regexp.MustCompile(`[[:punct:]\s]+`)
	cleaned = punctRe.ReplaceAllString(cleaned, "")
	
	return cleaned
}

// cleanOptions 清洗选项
func (s *ParserService) cleanOptions(options map[string]string) map[string]string {
	cleaned := make(map[string]string)
	punctRe := regexp.MustCompile(`[[:punct:]\s]+`)
	
	for key, value := range options {
		cleanedValue := punctRe.ReplaceAllString(value, "")
		cleaned[key] = cleanedValue
	}
	
	return cleaned
}

// generateHashKey 生成哈希键
func (s *ParserService) generateHashKey(content string) string {
	hash := md5.Sum([]byte(content))
	return fmt.Sprintf("%x", hash)
}

// ConvertToQuestionResponse 转换为API响应格式
func (s *ParserService) ConvertToQuestionResponse(questions []models.QuestionBank) []models.QuestionResponse {
	responses := make([]models.QuestionResponse, 0, len(questions))
	
	for _, q := range questions {
		// 解析选项
		var options map[string]string
		if err := json.Unmarshal(q.Options, &options); err != nil {
			options = make(map[string]string)
		}
		
		// 解析答案
		var answer interface{}
		if err := json.Unmarshal(q.Answer, &answer); err != nil {
			answer = ""
		}
		
		// 转换题目类型
		typeMap := map[int]string{
			1: "判断题",
			2: "单选题", 
			3: "多选题",
		}
		
		response := models.QuestionResponse{
			ID:       q.ID,
			Type:     typeMap[q.Type],
			Content:  q.Content,
			Options:  options,
			Answer:   answer,
			Analysis: q.Analysis,
			ImageURL: q.ImageURL,
			UserURL:  q.UserURL,
		}
		
		responses = append(responses, response)
	}
	
	return responses
}

// GetQuestionTypeCode 获取题目类型代码
func (s *ParserService) GetQuestionTypeCode(questionType string) int {
	switch questionType {
	case "判断题":
		return 1
	case "单选题":
		return 2
	case "多选题":
		return 3
	default:
		return 0
	}
}
