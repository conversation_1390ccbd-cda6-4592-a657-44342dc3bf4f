package middleware

import (
	"net/http"
	"solve-go-api/internal/services"
	"solve-go-api/internal/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// JWTAuth JWT认证中间件
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.<PERSON>("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "未提供认证令牌",
			})
			c.Abort()
			return
		}

		// 移除 "Bearer " 前缀
		if strings.HasPrefix(token, "Bearer ") {
			token = token[7:]
		}

		claims, err := utils.ParseJWT(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "无效的认证令牌",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("user_role", claims.Role)
		c.Next()
	}
}

// RoleAuth 角色认证中间件
func RoleAuth(allowedRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": "权限不足",
			})
			c.Abort()
			return
		}

		role := userRole.(string)
		for _, allowedRole := range allowedRoles {
			if role == allowedRole {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "权限不足",
		})
		c.Abort()
	}
}

// AppAuth 应用认证中间件
func AppAuth(appService *services.AppService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求体中获取认证信息
		var requestBody map[string]interface{}
		if err := c.ShouldBindJSON(&requestBody); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "请求参数错误",
			})
			c.Abort()
			return
		}

		appID, ok1 := requestBody["app_id"].(string)
		appSecret, ok2 := requestBody["app_secret"].(string)

		if !ok1 || !ok2 || appID == "" || appSecret == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "缺少必要的认证参数",
			})
			c.Abort()
			return
		}

		// 验证应用凭证
		app, err := appService.ValidateApp(appID, appSecret)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "应用认证失败",
			})
			c.Abort()
			return
		}

		// 检查应用状态
		if app.Status != 0 {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": "应用已被禁用",
			})
			c.Abort()
			return
		}

		// 获取用户信息
		user, err := appService.GetUserByID(app.UserID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取用户信息失败",
			})
			c.Abort()
			return
		}

		// 检查用户状态
		if !user.IsActive {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": "用户已被禁用",
			})
			c.Abort()
			return
		}

		// 将应用和用户信息存储到上下文
		c.Set("app", app)
		c.Set("user", user)
		c.Next()
	}
}
