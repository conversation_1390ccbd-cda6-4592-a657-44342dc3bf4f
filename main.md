# 使用Go开发一个web的api服务端；无需考虑前端开发，前端将有前端开发团队实现；

### 使用的技术套件
    - gin框架
    - mysql8
    - redis
    - 阿里云短信
    - 等其他套件



### 要求模块化 + 结构体封装 + 接口

### 业务角色权限描述
    - 角色有三种
        1. 超级管理员  // 对系统的完整控制权限，由系统初始化时直接创建，只有一个。密码设置为123456，账号为手机号随机设置
        2. 题库管理员  // 仅可对题库与用户搜题记录进行增删改查，由系统初始化时直接创建，只有两个。密码设置为123456，账号为手机号随机设置
        3. 普通用户    // 仅可创建应用，管理自己的应用，查看自己的应用的一些情况，用户自行注册。

    - 用户数据表字段设计 表名 hook_user
        id = 自增id；
        phone = 账号； //手机号
        password = 密码； 
        role = 角色；//admin、manager、user｜默认注册是user，其他两种不允许注册，是系统初始化的时候生成的。不允许新注册；
        nickname = 昵称；
        balance = 积分；相当于余额，用户所创建的应用在成功调用api的时候扣除用户的积分。
        is_active = 用户状态； // 0表示用户被禁用 1表示用户正常 
        created_at = 创建时间；
        updated_at = 最后一次登陆时间； 

    - 描述；这是个单层租户的框架，并不是真正意义的saas多租户框架。用户可以创建N个应用，但核心api业务是由应用为端点进行调用，每个应用每个应用都是独立计费、独立配置、独立参数，由用户账户统一扣费。


    - 描述；用户注册使用手机号+短信验证码+密码进行注册；注册后默认为禁用账户，需要管理员手动对其状态改变为启用后才可正常使用。

    - 描述；当用户积分不足时，api会返回失败；也就是说应用请求过来时api第一步是先校验应用所属用户的账户积分是否为<=0，如果<=0则直接返回失败，不会进行任何业务逻辑处理。所以用户积分允许是负数，比如用户当次请求消耗1700积分，但是用户余额只有1000积分，这个时候是允许出现负数的。因为具体消费多少积分，用户请求时并不知道，需要在业务处理完成后才可以知道用户到底积分消费是多少。

### 系统配置表 hook_system_config //预留后续使用，或者开发中如果有必要的参数，也可以规划进这个表。

    id = 自增id；
    key = 配置键名；
    value = 配置值；
    description = 配置描述；
    created_at = 配置创建时间；
    updated_at = 配置更新时间；


### 用户API业务

    - 超级管理员/题库管理员/用户都是使用一个api接口进行登陆的
    - 超级管理员的权限
        1. 系统参数配置，如模型参数设置、等一系列系统能在数据库配置中调控的参数配置
        2. 查看/冻结/解冻所有用户信息
        3. 查看/冻结/解冻所有用户的应用信息
        4. 给用户充值积分
        5. 另外还具备题库管理员以及用户的所有能力

    - 题库管理员的权限
        1. 对请求日志的查看与删除
        2. 对题库进行增删改查
        3. 只有这两个能力，没有其他能力
    
    - 用户的权限
        1. 创建应用
        2. 查看自己应用调用信息
        3. 查看自己的积分帐变信息
        4. 重置自己应用的App Secret
        5. 修改自己创建的应用的名称
        6. 使用手机号+短信验证码 修改登录密码

### 用户积分帐变表 hook_balance_logs

    id = 自增id；
    user_id = 关联的用户id；
    change_amount = 变化金额 //正负数体现充值与扣费
    reason = 描述 //如管理员充值、调用扣费
    operator_id = 操作人ID //如管理员、系统
    created_at = 发生时间



### 应用概念
    - 每个应用都有自己的App ID 和 App Secret
    - App ID 和 App Secret是调用系统api的唯一凭证
    - 应用每次调用API成功，都会对应用所属用户的账户进行扣费。扣费算法为，OCR模型返回的total_tokens * 2就是要扣出的积分。

### 应用表 hook_apps

    id = 自增id；
    user_id = 关联的用户id；
    name = 应用名称 //用户创建应用时设置的应用名称
    app_id = 应用id //系统自动生成的应用ID，16位大小写+数字的随机值，唯一性的
    app_secret = 应用私钥 //32位大小写字母+数字的随机值
    status = 应用状态 //0为正常 1为禁用。创建时默认为0  需要注意如果关联的用户处于禁用状态，用户的应用即使状态正常，应用也无法正常请求。
    total_calls = 累计总调用成功的次数
    created_at = 应用创建时间




### api核心搜题业务
    - api的请求参数
        1. app_id
        2. app_secret
        3. image_url //图片地址

    - api的响应参数
        1. code //状态码 200=成功 400=失败
        2. message //提示信息
        3. data //响应数据

    - api的响应数据 // 可能存在多个答案，所以无论是返回1个还是N个结果，都要数组形式返回
        [
        {
            "id": 10001,
            "type": "单选题",
            "content": "以下哪个是安全驾驶行为？",
            "options": {
            "A": "疲劳驾驶",
            "B": "酒后驾驶",
            "C": "系安全带",
            "D": "超速行驶"
            },
            "answer": "C",
            "analysis": "系安全带是安全驾驶的重要措施，其余行为都是严重违法行为。",
            "image_url": "https://example.com/q1.jpg",
            "user_url": "https://example.com/u1.jpg"
        },
        {
            "id": 10002,
            "type": "判断题",
            "content": "驾驶机动车遇前方拥堵时，应依次排队等候，不得穿插抢行。",
            "options": {
            "Y": "正确",
            "N": "错误"
            },
            "answer": "A",
            "analysis": "根据道路交通安全法规，该说法正确，抢行属于违法行为。",
            "image_url": "https://example.com/q2.jpg",
            "user_url": "https://example.com/u2.jpg"
        },
        {
            "id": 10003,
            "type": "多选题",
            "content": "以下属于文明驾驶行为的是？",
            "options": {
            "A": "乱鸣喇叭",
            "B": "主动礼让",
            "C": "按道行驶",
            "D": "使用远光灯强行超车"
            },
            "answer": ["B", "C"],
            "analysis": "主动礼让、按道行驶是文明驾驶表现，其他选项属不当行为。",
            "image_url": "https://example.com/q3.jpg",
            "user_url": "https://example.com/u3.jpg"
        }
        ]



    - api 业务逻辑

        1. 封装一个ProcessContext结构体用来贯穿本次请求的生命周期；先存储用户请求的图片url；存入SaveQuestion数组中的type字段。


        2. 将用户请求过来的图片url提交给OCR模型识别，识别后得到问题的类型、题干、选项等信息；
            //OCR模型请求哪个模型是通过环境变量设置的。
            //比如 model_OCR = qwen-vl-plus就从hook_models表内查询出name为qwen-vl-plus的模型的详细配置信息，
            //若环境设置配置的模型名称，mysql中不存在则返回；模型配置异常，请联系管理员。
            //相关模型的问题需参考model.md文档

        3. 将OCR模型响应的数据进行格式化解析  //方法命名ParseOCRResponse()
            - 从OCR模型响应数据中的qutext字段中提取我们需要的字段
            - 先从qutext中检测 是否存在 单选题｜判断题｜多选题 如果不存在则直返回“图片解析失败，请重新拍摄标准图片”
            - 若存在，则取出对应的问题类型存入ProcessContext内SaveQuestion数组中的type字段内。
            - 使用正则 ^(（?\(?\s*(单选题|多选题|判断题|填空题)?\)?）?\s*\d{1,3}[、.．]?\s*) 清洗qutext内容的前缀后将值存入存入SaveQuestion数组中的content字段内。
            - 使用正则 (`[[:punct:]\s]+`) 将SaveQuestion数组中的content字段值清洗掉所有换行符、空格、标点符号后存入SaveQuestion数组中的cleancontent字段内。 

            - 从响应数据中的options字段中提取我们需要的值
            - 使用正则 (`[[:punct:]\s]+`) 将options的选项值清洗掉标点符号、空格与换行符，对应存入SaveQuestion数组中的options字段内。

            - 从响应数据中的usage字段中提取我们需要的值total_tokens，//这个是请求日志中会用到，以及后续给应用用户扣费会使用。也需要存入ProcessContext。
        
        4. 缓存键名hash_key的制作
            - 将ProcessContext中SaveQuestion数组中的cleancontent字段值进行拼接，然后进行MD5处理成缓存键名hash_key
            //完全相同的内容哈希键名必须一致，不同的内容哈希键名必须不一致。
            //键名hash_key 存储在SaveQuestion数组中的hash_key字段内

        5. 计算SaveQuestion数组中的cleancontent字段的字符长度，并存储在SaveQuestion数组中的question_len字段内

        5. 使用hash_key对redis检索是否匹配键名，如果匹配则返回响应应用返回对应的值，如果没有匹配则执行第6步。

        6. 在mysql中查找是否有相同hash_key的记录，如果有则按照命中逻辑进行处理，如果没有则执行第7步。
            //可能mysql中存在多个相同hash_key的记录，需要将相同hash_key记录的值打包成数组返回给用户并回写redis
            //被动回写机制，利用mysql作为redis的持久化方案
            //回写方案参考给请求应用响应数据中的数组，回写redis时valve的值必须是响应给应用的数组。

        7. 对数据库进行二次检索，如果命中则响应数据给用户，未命中则进行第8步，
            // 检索方法命名MatchQuestion() 参考MatchQuestion.md文档
            //这一步如果命中并不会回写redis，只有第6步中以hash_key命中的才会回写redis

        8. 将ProcessContext内SaveQuestion数组中type/content/options的内容提交给solve模型。
            //solve模型请求哪个模型是通过环境变量设置的。
            //比如 model_solve = qwen-plus就从hook_models表内查询出name为qwen-plus的模型的详细配置信息，
            //若环境设置配置的模型名称，mysql中不存在则返回；模型配置异常，请联系管理员。
            //相关模型的问题需参考model.md文档

        9. 将solve模型响应的json数据进行格式化解析
            //解析后会得到2个值answer，analysis
            //存储到ProcessContext内SaveQuestion数组中answer与analysis字段内
            最终ProcessContext内SaveQuestion数组中的值为
            {
                type : "",
                concontent : "",
                cleancontent : "",
                options :  {
                    "" : "",
                    "" : "",
                    "" : "",
                    "" : ""
                },
                answer : {
                    "" : "",
                    "" : "",
                    "" : "",
                    "" : ""
                },
                analysis : "",
                user_url : "",
                question_len : "",
                hash_key : "",
            }

        10. 将ProcessContext内SaveQuestion的数据入库，并回写redis，
            //回写redis时valve的值必须是响应给应用的数组格式。
            //先检查mysql中是否存在相同hash_key的记录，如果存在则将所有相同的记录全部查询出来打包成数组。
            //用这个hash_key以及这个打包的数组组成键值对回写redis。

### 请求日志查询api

    1. 记录用户请求时间
    2. 记录应用请求的图片url
    3. 用户请求的应用app_id
    4. 响应的内容 //最终给应用响应返回的数组
    5. 响应状态 //是否成功响应，1=成功 0= 失败
    6. 响应耗时 //从请求到响应的耗时时长
    7. 请求的响应来源 //比如redis、mysql、Match、AI中是谁提供的答案
    8. OCR模型消耗的token。
    具体以下表为准


        - 请求记录表 hook_solve_logs

        id = 自增id；
        app_id = 应用id；
        user_url = 用户请求的图片；
        matched_id = 命中的问题ID // 如果有
        ocr_token = 识图模型消耗的token 
        source = 响应来源 //比如redis、mysql、Match、AI中是谁提供的答案
        data = 响应的内容 //最终给应用响应返回的数组
        status = 响应状态 //是否成功响应，1=成功 0= 失败
        latency = 响应耗时 //从请求到响应的耗时时长
        created_at = 请求的时间


### 题库表 hook_question_bank

id = 自增id；
hash_key = 哈希缓存键名
type = 问题类型；判断题 = 1 单选题 = 2 多选题 = 3
content = 题干内容；
content_clean = 清洗后的题干内容；
options = 问题选项；//json格式存储
answer = 问题答案；//json存储
analysis = 问题解析；
image_url = 题干图片； //默认为空 题库管理员维护添加
user_url = 用户图片； //请求API时上传的图片地址
verified = 验证状态；//0=未验证，1=已验证；默认为0
question_len = 字符串长度 //入库时生成，检索时使用
created_at = 问题创建时间；
updated_at = 问题更新时间；







